package interactor_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"sa-intranet/core"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/jira"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestJiraProjectsInteractorInitialDataComprehensive(t *testing.T) {
	tests := []struct {
		name     string
		filter   string
		page     int
		pageSize int
		wantErr  bool
		verify   func(t *testing.T, got interactor.JiraProjectsData, filter string, page int, pageSize int)
	}{
		{
			name:     "successful data retrieval with filter",
			filter:   "test",
			page:     1,
			pageSize: 10,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.JiraProjectsData, filter string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Erro<PERSON>("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
				if got.Pagination.PerPage != pageSize {
					t.Errorf("Expected pageSize %d, got %d", pageSize, got.Pagination.PerPage)
				}
			},
		},
		{
			name:     "successful data retrieval without filter",
			filter:   "",
			page:     1,
			pageSize: 20,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.JiraProjectsData, filter string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
				if got.Pagination.PerPage != pageSize {
					t.Errorf("Expected pageSize %d, got %d", pageSize, got.Pagination.PerPage)
				}
			},
		},
		{
			name:     "large page size",
			filter:   "",
			page:     2,
			pageSize: 100,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.JiraProjectsData, filter string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			inj := injector.Clone()
			usecase := do.MustInvoke[*interactor.JiraProjectsInteractor](inj)

			got, err := usecase.InitialData(tt.filter, tt.page, tt.pageSize)

			if tt.wantErr {
				if err == nil {
					t.Errorf("InitialData() expected error, got nil")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("InitialData() unexpected error = %v", err)
				return
			}

			// Standard verification
			if got.JiraProjects == nil {
				t.Error("Expected JiraProjects to be initialized")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.filter, tt.page, tt.pageSize)
			}
		})
	}
}

func setupJiraProjectMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch {
		case strings.Contains(r.URL.Path, "/rest/api/3/project/"):
			// Extract project key from URL
			projectKey := strings.TrimPrefix(r.URL.Path, "/rest/api/3/project/")

			response := jira.Project{
				ID:   "10001",
				Key:  projectKey,
				Name: "Test Project " + projectKey,
				IssueTypes: []jira.IssueType{
					{
						ID:   "10000",
						Name: "Task",
					},
					{
						ID:   "10001",
						Name: "Technical Debt",
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)

		case r.URL.Path == "/rest/api/3/issue":
			response := jira.CreateIssueResponse{
				ID:   "10001",
				Key:  "TEST-123",
				Self: "https://test.atlassian.net/rest/api/3/issue/10001",
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(response)

		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func TestJiraProjectsInteractorCreateJiraProject(t *testing.T) {
	// Setup mock server
	mockServer := setupJiraProjectMockServer()
	defer mockServer.Close()

	// Note: jira.ClientConfig is empty struct, no need to override
	inj := injector.Clone()

	// Create test company client
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](inj)
	companyClient := &model.CompanyClient{
		Name: "Test Company",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	active := true
	tests := []struct {
		name        string
		input       interactor.JiraProjectValidator
		wantErr     bool
		errContains string
		verify      func(t *testing.T, got interactor.JiraProject, input interactor.JiraProjectValidator)
	}{
		{
			name: "successful project creation",
			input: interactor.JiraProjectValidator{
				ProjectKey:      "TEST",
				Token:           "test-token",
				Username:        "<EMAIL>",
				JiraURL:         mockServer.URL,
				Active:          &active,
				CompanyClientID: savedClient.ID,
			},
			wantErr: false,
			verify: func(t *testing.T, got interactor.JiraProject, input interactor.JiraProjectValidator) {
				if got.ProjectKey != input.ProjectKey {
					t.Errorf("Expected ProjectKey %s, got %s", input.ProjectKey, got.ProjectKey)
				}
				if got.Username != input.Username {
					t.Errorf("Expected Username %s, got %s", input.Username, got.Username)
				}
				if got.Active != *input.Active {
					t.Errorf("Expected Active %v, got %v", *input.Active, got.Active)
				}
				if got.Name == "" {
					t.Error("Expected Name to be populated from Jira API")
				}
			},
		},
		{
			name: "validation error - missing project key",
			input: interactor.JiraProjectValidator{
				ProjectKey:      "",
				Token:           "test-token",
				Username:        "<EMAIL>",
				JiraURL:         mockServer.URL,
				Active:          &active,
				CompanyClientID: savedClient.ID,
			},
			wantErr:     true,
			errContains: "validation",
		},
		{
			name: "validation error - invalid email",
			input: interactor.JiraProjectValidator{
				ProjectKey:      "TEST",
				Token:           "test-token",
				Username:        "invalid-email",
				JiraURL:         mockServer.URL,
				Active:          &active,
				CompanyClientID: savedClient.ID,
			},
			wantErr:     true,
			errContains: "validation",
		},
		{
			name: "validation error - invalid URL",
			input: interactor.JiraProjectValidator{
				ProjectKey:      "TEST",
				Token:           "test-token",
				Username:        "<EMAIL>",
				JiraURL:         "invalid-url",
				Active:          &active,
				CompanyClientID: savedClient.ID,
			},
			wantErr:     true,
			errContains: "validation",
		},
		{
			name: "validation error - missing company client ID",
			input: interactor.JiraProjectValidator{
				ProjectKey:      "TEST",
				Token:           "test-token",
				Username:        "<EMAIL>",
				JiraURL:         mockServer.URL,
				Active:          &active,
				CompanyClientID: uuid.Nil,
			},
			wantErr:     true,
			errContains: "validation",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.JiraProjectsInteractor](inj)

			got, validationErrors, err := usecase.CreateJiraProject(tt.input)

			if tt.wantErr {
				if err == nil {
					t.Errorf("CreateJiraProject() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("CreateJiraProject() error = %v, should contain %v", err, tt.errContains)
				}
				if validationErrors == nil && strings.Contains(tt.errContains, "validation") {
					t.Error("Expected validation errors to be returned")
				}
				return
			}

			if err != nil {
				t.Errorf("CreateJiraProject() unexpected error = %v", err)
				return
			}

			if validationErrors != nil {
				t.Errorf("CreateJiraProject() unexpected validation errors = %v", validationErrors)
				return
			}

			// Standard verification
			if got.ID == uuid.Nil {
				t.Error("Expected ID to be set")
			}
			if got.CreatedAt.IsZero() {
				t.Error("Expected CreatedAt to be set")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}

func TestJiraProjectsInteractorUpdateJiraProject(t *testing.T) {
	// Setup mock server
	mockServer := setupJiraProjectMockServer()
	defer mockServer.Close()

	// Note: jira.ClientConfig is empty struct, no need to override
	inj := injector.Clone()

	// Create test company client
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](inj)
	companyClient := &model.CompanyClient{
		Name: "Test Company",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	// Create test Jira project
	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](inj)
	cypher := do.MustInvoke[core.Cypher](inj)
	encryptedToken, err := cypher.Encrypt("original-token")
	if err != nil {
		t.Fatalf("Failed to encrypt token: %v", err)
	}

	jiraProject := &model.JiraProject{
		ProjectKey:      "ORIG",
		Name:            "Original Project",
		JiraURL:         mockServer.URL,
		Username:        "<EMAIL>",
		Active:          true,
		Token:           encryptedToken,
		CompanyClientID: savedClient.ID,
	}
	savedProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	active := false
	tests := []struct {
		name        string
		projectID   string
		input       interactor.JiraProjectUpdateValidator
		wantErr     bool
		errContains string
		verify      func(t *testing.T, got interactor.JiraProject, input interactor.JiraProjectUpdateValidator)
	}{
		{
			name:      "successful project update with new token",
			projectID: savedProject.ID.String(),
			input: interactor.JiraProjectUpdateValidator{
				ProjectKey:      "UPDATED",
				Token:           "new-token",
				Username:        "<EMAIL>",
				Active:          &active,
				JiraURL:         mockServer.URL,
				CompanyClientID: savedClient.ID,
			},
			wantErr: false,
			verify: func(t *testing.T, got interactor.JiraProject, input interactor.JiraProjectUpdateValidator) {
				if got.ProjectKey != input.ProjectKey {
					t.Errorf("Expected ProjectKey %s, got %s", input.ProjectKey, got.ProjectKey)
				}
				if got.Username != input.Username {
					t.Errorf("Expected Username %s, got %s", input.Username, got.Username)
				}
				if got.Active != *input.Active {
					t.Errorf("Expected Active %v, got %v", *input.Active, got.Active)
				}
			},
		},
		{
			name:      "successful project update without new token",
			projectID: savedProject.ID.String(),
			input: interactor.JiraProjectUpdateValidator{
				ProjectKey:      "UPDATED2",
				Token:           "", // Empty token should use existing
				Username:        "<EMAIL>",
				Active:          &active,
				JiraURL:         mockServer.URL,
				CompanyClientID: savedClient.ID,
			},
			wantErr: false,
			verify: func(t *testing.T, got interactor.JiraProject, input interactor.JiraProjectUpdateValidator) {
				if got.ProjectKey != input.ProjectKey {
					t.Errorf("Expected ProjectKey %s, got %s", input.ProjectKey, got.ProjectKey)
				}
			},
		},
		{
			name:        "invalid project ID",
			projectID:   "invalid-uuid",
			input:       interactor.JiraProjectUpdateValidator{},
			wantErr:     true,
			errContains: "parse",
		},
		{
			name:      "validation error - invalid email",
			projectID: savedProject.ID.String(),
			input: interactor.JiraProjectUpdateValidator{
				ProjectKey:      "TEST",
				Username:        "invalid-email",
				Active:          &active,
				JiraURL:         mockServer.URL,
				CompanyClientID: savedClient.ID,
			},
			wantErr:     true,
			errContains: "validation",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.JiraProjectsInteractor](inj)

			got, validationErrors, err := usecase.UpdateJiraProject(tt.projectID, tt.input)

			if tt.wantErr {
				if err == nil {
					t.Errorf("UpdateJiraProject() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("UpdateJiraProject() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("UpdateJiraProject() unexpected error = %v", err)
				return
			}

			if validationErrors != nil {
				t.Errorf("UpdateJiraProject() unexpected validation errors = %v", validationErrors)
				return
			}

			// Standard verification
			if got.ID == uuid.Nil {
				t.Error("Expected ID to be set")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}
