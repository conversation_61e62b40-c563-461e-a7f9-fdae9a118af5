package interactor

import (
	"context"
	"errors"
	"fmt"

	corerepository "sa-intranet/core/repository"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type CompanyClientsData struct {
	CompanyClients []CompanyClient `json:"companyClients"`
	Pagination     Pagination      `json:"pagination"`
}

type CompanyClient struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
}

type CompanyClientsInteractor struct {
	CompanyClientRepo repository.CompanyClientRepository
	validator         *validator.Validate
}

type CompanyClientValidator struct {
	Name string `json:"name" validate:"required"`
}

func NewCompanyClientsInteractor(i *do.Injector) (*CompanyClientsInteractor, error) {
	CompanyClientRepo, err := do.Invoke[repository.CompanyClientRepository](i)
	if err != nil {
		return nil, err
	}

	validator, err := do.Invoke[*validator.Validate](i)
	if err != nil {
		return nil, err
	}

	return &CompanyClientsInteractor{
		CompanyClientRepo: CompanyClientRepo,
		validator:         validator,
	}, nil
}

func (i *CompanyClientsInteractor) InitialData(filter string, page int, pageSize int) (CompanyClientsData, error) {
	var data CompanyClientsData
	ctx := context.Background()

	params := corerepository.PaginationParams[repository.CompanyClientFilter]{
		Filters: repository.CompanyClientFilter{
			Name: filter,
		},
		Page:     page,
		PageSize: pageSize,
	}

	companyClientsResp, err := i.CompanyClientRepo.List(ctx, params)
	if err != nil {
		return data, err
	}

	companyClients := make([]CompanyClient, 0, len(companyClientsResp.Items))

	for _, client := range companyClientsResp.Items {
		companyClients = append(companyClients, CompanyClient{
			ID:   client.ID,
			Name: client.Name,
		})
	}

	pagination := Pagination{
		TotalCount:  int(companyClientsResp.TotalItems),
		CurrentPage: companyClientsResp.Page,
		PerPage:     companyClientsResp.PageSize,
	}

	return CompanyClientsData{
		Pagination:     pagination,
		CompanyClients: companyClients,
	}, nil
}

func (i *CompanyClientsInteractor) CreateCompanyClient(companyClient CompanyClientValidator) (CompanyClient, map[string]any, error) {
	var validationErrors map[string]any
	companyClientResp := CompanyClient{}

	err := i.validator.Struct(companyClient)
	if err != nil {
		var validationErr validator.ValidationErrors

		ok := errors.As(err, &validationErr)
		if !ok {
			return companyClientResp, nil, fmt.Errorf("failed to validate company client: %w", err)
		}

		validationErrors = validatorext.FormatFormErrors(companyClient, validationErr)

		return companyClientResp, validationErrors, ErrValidationFailed
	}

	client := &model.CompanyClient{
		Name: companyClient.Name,
	}

	companyClientDB, saveErr := i.CompanyClientRepo.Save(context.Background(), client)
	if saveErr != nil {
		return companyClientResp, nil, saveErr
	}

	companyClientResp = CompanyClient{
		ID:   companyClientDB.ID,
		Name: companyClientDB.Name,
	}

	return companyClientResp, nil, nil
}

func (i *CompanyClientsInteractor) UpdateCompanyClient(companyclientID string, companyClient CompanyClientValidator) (CompanyClient, map[string]any, error) {
	var validationErrors map[string]any
	ctx := context.Background()
	companyClientResp := CompanyClient{}

	clientUUID, err := uuid.Parse(companyclientID)
	if err != nil {
		return companyClientResp, nil, err
	}

	companyClientFromDB, err := i.CompanyClientRepo.Find(ctx, clientUUID)
	if err != nil {
		return companyClientResp, nil, err
	}

	err = i.validator.Struct(companyClient)
	if err != nil {
		var validationErr validator.ValidationErrors

		ok := errors.As(err, &validationErr)
		if !ok {
			return companyClientResp, nil, fmt.Errorf("failed to validate company client: %w", err)
		}

		validationErrors = validatorext.FormatFormErrors(companyClient, validationErr)

		return companyClientResp, validationErrors, ErrValidationFailed
	}

	client := &model.CompanyClient{
		ID:        companyClientFromDB.ID,
		Name:      companyClient.Name,
		CreatedAt: companyClientFromDB.CreatedAt,
	}

	client, saveErr := i.CompanyClientRepo.Save(context.Background(), client)
	if saveErr != nil {
		return companyClientResp, nil, saveErr
	}

	companyClientResp = CompanyClient{
		ID:   client.ID,
		Name: client.Name,
	}

	return companyClientResp, nil, nil
}
